# Database Configuration - H2 for development
spring.datasource.url=jdbc:h2:mem:hrmsdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password

# H2 Console (for development)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.properties.hibernate.format_sql=true

# Disable SQL initialization
spring.sql.init.mode=never

# Server Configuration
server.port=8080
server.servlet.context-path=/hrms

# JWT Configuration
jwt.secret=hrmsSecretKey2024ForAuthenticationAndAuthorizationWithSecureLength512BitsMinimumRequiredForHS512Algorithm
jwt.expiration=86400000

# Thymeleaf Configuration
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# Static Resources
spring.web.resources.static-locations=classpath:/static/

# Logging
logging.level.com.hrms=DEBUG
logging.level.org.springframework.security=DEBUG
