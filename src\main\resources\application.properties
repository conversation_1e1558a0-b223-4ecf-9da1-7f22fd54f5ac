# Database Configuration
spring.datasource.url=*************************************************************************************************
spring.datasource.username=root
spring.datasource.password=password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=true

# Server Configuration
server.port=8080
server.servlet.context-path=/hrms

# JWT Configuration
jwt.secret=hrmsSecretKey2024ForAuthenticationAndAuthorization
jwt.expiration=86400000

# Thymeleaf Configuration
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# Static Resources
spring.web.resources.static-locations=classpath:/static/

# Logging
logging.level.com.hrms=DEBUG
logging.level.org.springframework.security=DEBUG
