package com.hrms.repository;

import com.hrms.model.JobPosting;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface JobPostingRepository extends JpaRepository<JobPosting, Long> {
    
    List<JobPosting> findByStatus(JobPosting.JobStatus status);
    
    List<JobPosting> findByDepartment(String department);
    
    List<JobPosting> findByEmploymentType(JobPosting.EmploymentType employmentType);
    
    @Query("SELECT jp FROM JobPosting jp WHERE jp.jobTitle LIKE %:keyword% OR jp.department LIKE %:keyword% OR jp.location LIKE %:keyword%")
    List<JobPosting> searchJobPostings(@Param("keyword") String keyword);
    
    @Query("SELECT jp FROM JobPosting jp WHERE jp.status = 'ACTIVE' AND (jp.closingDate IS NULL OR jp.closingDate >= :currentDate)")
    List<JobPosting> findActiveJobPostings(@Param("currentDate") LocalDate currentDate);
    
    @Query("SELECT jp FROM JobPosting jp WHERE jp.postedBy = :userId")
    List<JobPosting> findByPostedBy(@Param("userId") Long userId);
    
    @Query("SELECT COUNT(jp) FROM JobPosting jp WHERE jp.department = :department AND jp.status = 'ACTIVE'")
    Long countActiveJobsByDepartment(@Param("department") String department);
}
