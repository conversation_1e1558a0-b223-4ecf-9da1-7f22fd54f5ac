package com.hrms.repository;

import com.hrms.model.JobApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface JobApplicationRepository extends JpaRepository<JobApplication, Long> {
    
    List<JobApplication> findByJobPostingId(Long jobPostingId);
    
    List<JobApplication> findByStatus(JobApplication.ApplicationStatus status);
    
    List<JobApplication> findByEmail(String email);
    
    @Query("SELECT ja FROM JobApplication ja WHERE ja.firstName LIKE %:keyword% OR ja.lastName LIKE %:keyword% OR ja.email LIKE %:keyword%")
    List<JobApplication> searchApplications(@Param("keyword") String keyword);
    
    @Query("SELECT ja FROM JobApplication ja WHERE ja.jobPosting.id = :jobPostingId AND ja.status = :status")
    List<JobApplication> findByJobPostingAndStatus(@Param("jobPostingId") Long jobPostingId, 
                                                  @Param("status") JobApplication.ApplicationStatus status);
    
    @Query("SELECT ja FROM JobApplication ja WHERE ja.reviewedBy = :reviewerId")
    List<JobApplication> findByReviewedBy(@Param("reviewerId") Long reviewerId);
    
    @Query("SELECT COUNT(ja) FROM JobApplication ja WHERE ja.jobPosting.id = :jobPostingId")
    Long countApplicationsByJobPosting(@Param("jobPostingId") Long jobPostingId);
    
    @Query("SELECT COUNT(ja) FROM JobApplication ja WHERE ja.status = :status")
    Long countApplicationsByStatus(@Param("status") JobApplication.ApplicationStatus status);
}
