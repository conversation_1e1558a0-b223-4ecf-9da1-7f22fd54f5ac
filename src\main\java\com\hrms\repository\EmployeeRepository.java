package com.hrms.repository;

import com.hrms.model.Employee;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EmployeeRepository extends JpaRepository<Employee, Long> {
    
    Optional<Employee> findByEmployeeId(String employeeId);
    
    List<Employee> findByDepartment(String department);
    
    List<Employee> findByManagerId(Long managerId);
    
    List<Employee> findByEmploymentStatus(Employee.EmploymentStatus status);
    
    @Query("SELECT e FROM Employee e WHERE e.firstName LIKE %:keyword% OR e.lastName LIKE %:keyword% OR e.employeeId LIKE %:keyword%")
    List<Employee> searchEmployees(@Param("keyword") String keyword);
    
    @Query("SELECT e FROM Employee e WHERE e.department = :department AND e.employmentStatus = :status")
    List<Employee> findByDepartmentAndStatus(@Param("department") String department, 
                                           @Param("status") Employee.EmploymentStatus status);
    
    @Query("SELECT COUNT(e) FROM Employee e WHERE e.department = :department")
    Long countByDepartment(@Param("department") String department);
    
    @Query("SELECT DISTINCT e.department FROM Employee e ORDER BY e.department")
    List<String> findAllDepartments();
}
