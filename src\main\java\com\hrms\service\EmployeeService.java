package com.hrms.service;

import com.hrms.model.Employee;
import com.hrms.repository.EmployeeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class EmployeeService {

    @Autowired
    private EmployeeRepository employeeRepository;

    public List<Employee> getAllEmployees() {
        return employeeRepository.findAll();
    }

    public Optional<Employee> getEmployeeById(Long id) {
        return employeeRepository.findById(id);
    }

    public Optional<Employee> getEmployeeByEmployeeId(String employeeId) {
        return employeeRepository.findByEmployeeId(employeeId);
    }

    public Employee createEmployee(Employee employee) {
        // Generate employee ID if not provided
        if (employee.getEmployeeId() == null || employee.getEmployeeId().isEmpty()) {
            employee.setEmployeeId(generateEmployeeId());
        }
        return employeeRepository.save(employee);
    }

    public Employee updateEmployee(Employee employee) {
        return employeeRepository.save(employee);
    }

    public void deleteEmployee(Long id) {
        employeeRepository.deleteById(id);
    }

    public List<Employee> getEmployeesByDepartment(String department) {
        return employeeRepository.findByDepartment(department);
    }

    public List<Employee> getEmployeesByManager(Long managerId) {
        return employeeRepository.findByManagerId(managerId);
    }

    public List<Employee> getEmployeesByStatus(Employee.EmploymentStatus status) {
        return employeeRepository.findByEmploymentStatus(status);
    }

    public List<Employee> searchEmployees(String keyword) {
        return employeeRepository.searchEmployees(keyword);
    }

    public List<Employee> getEmployeesByDepartmentAndStatus(String department, Employee.EmploymentStatus status) {
        return employeeRepository.findByDepartmentAndStatus(department, status);
    }

    public Long getEmployeeCountByDepartment(String department) {
        return employeeRepository.countByDepartment(department);
    }

    public List<String> getAllDepartments() {
        return employeeRepository.findAllDepartments();
    }

    public Employee terminateEmployee(Long id) {
        Employee employee = employeeRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Employee not found"));
        employee.setEmploymentStatus(Employee.EmploymentStatus.TERMINATED);
        employee.setTerminationDate(java.time.LocalDate.now());
        return employeeRepository.save(employee);
    }

    public Employee reactivateEmployee(Long id) {
        Employee employee = employeeRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Employee not found"));
        employee.setEmploymentStatus(Employee.EmploymentStatus.ACTIVE);
        employee.setTerminationDate(null);
        return employeeRepository.save(employee);
    }

    private String generateEmployeeId() {
        // Simple employee ID generation - you can make this more sophisticated
        long count = employeeRepository.count();
        return String.format("EMP%05d", count + 1);
    }

    public boolean isManager(Long employeeId) {
        return employeeRepository.findByManagerId(employeeId).size() > 0;
    }

    public List<Employee> getDirectReports(Long managerId) {
        return employeeRepository.findByManagerId(managerId);
    }
}
