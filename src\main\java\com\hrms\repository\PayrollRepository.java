package com.hrms.repository;

import com.hrms.model.Payroll;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface PayrollRepository extends JpaRepository<Payroll, Long> {
    
    List<Payroll> findByEmployeeId(Long employeeId);
    
    List<Payroll> findByStatus(Payroll.PayrollStatus status);
    
    @Query("SELECT p FROM Payroll p WHERE p.payPeriodStart >= :startDate AND p.payPeriodEnd <= :endDate")
    List<Payroll> findByPayPeriod(@Param("startDate") LocalDate startDate, 
                                 @Param("endDate") LocalDate endDate);
    
    @Query("SELECT p FROM Payroll p WHERE p.employee.id = :employeeId AND p.payPeriodStart >= :startDate AND p.payPeriodEnd <= :endDate")
    List<Payroll> findByEmployeeAndPayPeriod(@Param("employeeId") Long employeeId,
                                           @Param("startDate") LocalDate startDate,
                                           @Param("endDate") LocalDate endDate);
    
    @Query("SELECT p FROM Payroll p WHERE p.employee.id = :employeeId AND p.payPeriodStart = :startDate AND p.payPeriodEnd = :endDate")
    Optional<Payroll> findByEmployeeAndExactPayPeriod(@Param("employeeId") Long employeeId,
                                                     @Param("startDate") LocalDate startDate,
                                                     @Param("endDate") LocalDate endDate);
    
    @Query("SELECT p FROM Payroll p WHERE p.employee.department = :department AND p.payPeriodStart >= :startDate AND p.payPeriodEnd <= :endDate")
    List<Payroll> findByDepartmentAndPayPeriod(@Param("department") String department,
                                             @Param("startDate") LocalDate startDate,
                                             @Param("endDate") LocalDate endDate);
}
