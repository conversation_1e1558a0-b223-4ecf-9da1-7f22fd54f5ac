<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HRMS - Human Resource Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">HRMS</h4>
                        <p class="text-muted">Human Resource Management</p>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link text-white" href="#dashboard" onclick="showSection('dashboard')">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="#employees" onclick="showSection('employees')">
                                <i class="fas fa-users me-2"></i>
                                Employees
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="#payroll" onclick="showSection('payroll')">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                Payroll
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="#leaves" onclick="showSection('leaves')">
                                <i class="fas fa-calendar-alt me-2"></i>
                                Leave Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="#recruitment" onclick="showSection('recruitment')">
                                <i class="fas fa-user-plus me-2"></i>
                                Recruitment
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="#reports" onclick="showSection('reports')">
                                <i class="fas fa-chart-bar me-2"></i>
                                Reports
                            </a>
                        </li>
                    </ul>
                    
                    <div class="mt-auto">
                        <hr class="text-white">
                        <div class="dropdown">
                            <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" 
                               id="dropdownUser1" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user-circle me-2"></i>
                                <span id="username">User</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-dark text-small shadow">
                                <li><a class="dropdown-item" href="#profile">Profile</a></li>
                                <li><a class="dropdown-item" href="#settings">Settings</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="logout()">Sign out</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- Login Section -->
                <div id="login-section" class="section">
                    <div class="row justify-content-center">
                        <div class="col-md-6 col-lg-4">
                            <div class="card shadow">
                                <div class="card-body">
                                    <div class="text-center mb-4">
                                        <h2>HRMS Login</h2>
                                        <p class="text-muted">Human Resource Management System</p>
                                    </div>
                                    
                                    <form id="loginForm">
                                        <div class="mb-3">
                                            <label for="username" class="form-label">Username</label>
                                            <input type="text" class="form-control" id="loginUsername" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="password" class="form-label">Password</label>
                                            <input type="password" class="form-control" id="loginPassword" required>
                                        </div>
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-primary">Login</button>
                                        </div>
                                    </form>
                                    
                                    <div class="text-center mt-3">
                                        <small class="text-muted">
                                            Demo credentials: admin/password, hr/password, manager/password
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Section -->
                <div id="dashboard" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">Dashboard</h1>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3 mb-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="totalEmployees">0</h4>
                                            <p>Total Employees</p>
                                        </div>
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="activeEmployees">0</h4>
                                            <p>Active Employees</p>
                                        </div>
                                        <i class="fas fa-user-check fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="pendingLeaves">0</h4>
                                            <p>Pending Leaves</p>
                                        </div>
                                        <i class="fas fa-calendar-times fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="openPositions">0</h4>
                                            <p>Open Positions</p>
                                        </div>
                                        <i class="fas fa-briefcase fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Recent Activities</h5>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled" id="recentActivities">
                                        <li class="mb-2">
                                            <i class="fas fa-user-plus text-success me-2"></i>
                                            New employee John Doe added
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-calendar text-warning me-2"></i>
                                            Leave request from Jane Smith pending
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-money-bill text-info me-2"></i>
                                            Payroll processed for March 2024
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Department Overview</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="departmentChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other sections will be added here -->
                <div id="employees" class="section" style="display: none;">
                    <h2>Employee Management</h2>
                    <p>Employee management functionality will be implemented here.</p>
                </div>

                <div id="payroll" class="section" style="display: none;">
                    <h2>Payroll Management</h2>
                    <p>Payroll management functionality will be implemented here.</p>
                </div>

                <div id="leaves" class="section" style="display: none;">
                    <h2>Leave Management</h2>
                    <p>Leave management functionality will be implemented here.</p>
                </div>

                <div id="recruitment" class="section" style="display: none;">
                    <h2>Recruitment Management</h2>
                    <p>Recruitment management functionality will be implemented here.</p>
                </div>

                <div id="reports" class="section" style="display: none;">
                    <h2>Reports</h2>
                    <p>Reports functionality will be implemented here.</p>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/app.js"></script>
</body>
</html>
