package com.hrms.config;

import com.hrms.model.Role;
import com.hrms.model.User;
import com.hrms.model.Employee;
import com.hrms.repository.RoleRepository;
import com.hrms.repository.UserRepository;
import com.hrms.repository.EmployeeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        initializeRoles();
        initializeUsers();
    }

    private void initializeRoles() {
        // Create roles if they don't exist
        if (roleRepository.findByName(Role.RoleName.ROLE_ADMIN).isEmpty()) {
            roleRepository.save(new Role(Role.RoleName.ROLE_ADMIN));
        }
        if (roleRepository.findByName(Role.RoleName.ROLE_HR).isEmpty()) {
            roleRepository.save(new Role(Role.RoleName.ROLE_HR));
        }
        if (roleRepository.findByName(Role.RoleName.ROLE_MANAGER).isEmpty()) {
            roleRepository.save(new Role(Role.RoleName.ROLE_MANAGER));
        }
        if (roleRepository.findByName(Role.RoleName.ROLE_EMPLOYEE).isEmpty()) {
            roleRepository.save(new Role(Role.RoleName.ROLE_EMPLOYEE));
        }
    }

    private void initializeUsers() {
        // Create admin user
        if (!userRepository.existsByUsername("admin")) {
            User admin = new User("admin", "<EMAIL>", passwordEncoder.encode("password"));
            Set<Role> adminRoles = new HashSet<>();
            adminRoles.add(roleRepository.findByName(Role.RoleName.ROLE_ADMIN).get());
            admin.setRoles(adminRoles);
            userRepository.save(admin);

            // Create admin employee record
            Employee adminEmployee = new Employee();
            adminEmployee.setEmployeeId("EMP00001");
            adminEmployee.setFirstName("System");
            adminEmployee.setLastName("Administrator");
            adminEmployee.setDateOfBirth(LocalDate.of(1980, 1, 1));
            adminEmployee.setGender(Employee.Gender.OTHER);
            adminEmployee.setPhoneNumber("************");
            adminEmployee.setAddress("123 Admin Street");
            adminEmployee.setHireDate(LocalDate.now());
            adminEmployee.setJobTitle("System Administrator");
            adminEmployee.setDepartment("IT");
            adminEmployee.setSalary(new BigDecimal("100000"));
            adminEmployee.setEmploymentStatus(Employee.EmploymentStatus.ACTIVE);
            adminEmployee.setUser(admin);
            employeeRepository.save(adminEmployee);
        }

        // Create HR user
        if (!userRepository.existsByUsername("hr")) {
            User hr = new User("hr", "<EMAIL>", passwordEncoder.encode("password"));
            Set<Role> hrRoles = new HashSet<>();
            hrRoles.add(roleRepository.findByName(Role.RoleName.ROLE_HR).get());
            hr.setRoles(hrRoles);
            userRepository.save(hr);

            // Create HR employee record
            Employee hrEmployee = new Employee();
            hrEmployee.setEmployeeId("EMP00002");
            hrEmployee.setFirstName("HR");
            hrEmployee.setLastName("Manager");
            hrEmployee.setDateOfBirth(LocalDate.of(1985, 5, 15));
            hrEmployee.setGender(Employee.Gender.FEMALE);
            hrEmployee.setPhoneNumber("************");
            hrEmployee.setAddress("456 HR Avenue");
            hrEmployee.setHireDate(LocalDate.now());
            hrEmployee.setJobTitle("HR Manager");
            hrEmployee.setDepartment("Human Resources");
            hrEmployee.setSalary(new BigDecimal("80000"));
            hrEmployee.setEmploymentStatus(Employee.EmploymentStatus.ACTIVE);
            hrEmployee.setUser(hr);
            employeeRepository.save(hrEmployee);
        }

        // Create Manager user
        if (!userRepository.existsByUsername("manager")) {
            User manager = new User("manager", "<EMAIL>", passwordEncoder.encode("password"));
            Set<Role> managerRoles = new HashSet<>();
            managerRoles.add(roleRepository.findByName(Role.RoleName.ROLE_MANAGER).get());
            manager.setRoles(managerRoles);
            userRepository.save(manager);

            // Create Manager employee record
            Employee managerEmployee = new Employee();
            managerEmployee.setEmployeeId("EMP00003");
            managerEmployee.setFirstName("Department");
            managerEmployee.setLastName("Manager");
            managerEmployee.setDateOfBirth(LocalDate.of(1982, 8, 20));
            managerEmployee.setGender(Employee.Gender.MALE);
            managerEmployee.setPhoneNumber("************");
            managerEmployee.setAddress("789 Manager Lane");
            managerEmployee.setHireDate(LocalDate.now());
            managerEmployee.setJobTitle("Department Manager");
            managerEmployee.setDepartment("Operations");
            managerEmployee.setSalary(new BigDecimal("90000"));
            managerEmployee.setEmploymentStatus(Employee.EmploymentStatus.ACTIVE);
            managerEmployee.setUser(manager);
            employeeRepository.save(managerEmployee);
        }

        // Create Employee user
        if (!userRepository.existsByUsername("employee")) {
            User employee = new User("employee", "<EMAIL>", passwordEncoder.encode("password"));
            Set<Role> employeeRoles = new HashSet<>();
            employeeRoles.add(roleRepository.findByName(Role.RoleName.ROLE_EMPLOYEE).get());
            employee.setRoles(employeeRoles);
            userRepository.save(employee);

            // Create Employee record
            Employee emp = new Employee();
            emp.setEmployeeId("EMP00004");
            emp.setFirstName("John");
            emp.setLastName("Doe");
            emp.setDateOfBirth(LocalDate.of(1990, 3, 10));
            emp.setGender(Employee.Gender.MALE);
            emp.setPhoneNumber("************");
            emp.setAddress("321 Employee Street");
            emp.setHireDate(LocalDate.now());
            emp.setJobTitle("Software Developer");
            emp.setDepartment("IT");
            emp.setSalary(new BigDecimal("70000"));
            emp.setEmploymentStatus(Employee.EmploymentStatus.ACTIVE);
            emp.setUser(employee);
            employeeRepository.save(emp);
        }
    }
}
