# HRMS - Human Resource Management System

A comprehensive Human Resource Management System built with Java Spring Boot, MySQL, and modern web technologies.

## Features

### Core Modules
- **Employee Management**: Complete employee lifecycle management
- **Payroll Processing**: Automated salary calculations and payslip generation
- **Leave Management**: Leave requests, approvals, and balance tracking
- **Recruitment**: Job postings, applications, and candidate tracking
- **Performance Management**: Employee evaluations and goal tracking
- **Reports & Analytics**: Comprehensive HR reports and dashboards

### Key Features
- Role-based access control (Ad<PERSON>, HR, Manager, Employee)
- Secure authentication with JWT tokens
- Responsive web interface
- RESTful API architecture
- Real-time dashboard with analytics
- Automated payroll calculations
- Leave balance tracking
- Recruitment workflow management

## Technology Stack

### Backend
- **Java 17**
- **Spring Boot 3.2.0**
- **Spring Security** (JWT Authentication)
- **Spring Data JPA** (Database ORM)
- **MySQL 8.0** (Primary Database)
- **Maven** (Dependency Management)

### Frontend
- **HTML5/CSS3**
- **JavaScript (ES6+)**
- **Bootstrap 5** (UI Framework)
- **Chart.js** (Data Visualization)
- **Font Awesome** (Icons)

## Prerequisites

Before running the application, ensure you have:

1. **Java 17** or higher installed
2. **Maven 3.6+** installed
3. **MySQL 8.0+** installed and running
4. **Git** for version control

## Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd HRMS
```

### 2. Database Setup
1. Start MySQL server
2. Create a database named `hrms_db`:
```sql
CREATE DATABASE hrms_db;
```

3. Update database configuration in `src/main/resources/application.properties`:
```properties
spring.datasource.url=*************************************************************************************************
spring.datasource.username=your_mysql_username
spring.datasource.password=your_mysql_password
```

### 3. Build and Run
```bash
# Build the application
mvn clean install

# Run the application
mvn spring-boot:run
```

The application will start on `http://localhost:8080/hrms`

## Default Login Credentials

The system comes with pre-configured demo accounts:

| Role | Username | Password | Description |
|------|----------|----------|-------------|
| Admin | admin | password | System Administrator |
| HR | hr | password | HR Manager |
| Manager | manager | password | Department Manager |
| Employee | employee | password | Regular Employee |

## API Documentation

### Authentication Endpoints
- `POST /api/auth/signin` - User login
- `POST /api/auth/signup` - User registration

### Employee Endpoints
- `GET /api/employees` - Get all employees
- `GET /api/employees/{id}` - Get employee by ID
- `POST /api/employees` - Create new employee
- `PUT /api/employees/{id}` - Update employee
- `DELETE /api/employees/{id}` - Delete employee

### Leave Management Endpoints
- `GET /api/leaves` - Get leave requests
- `POST /api/leaves` - Submit leave request
- `PUT /api/leaves/{id}/approve` - Approve leave request
- `PUT /api/leaves/{id}/reject` - Reject leave request

### Payroll Endpoints
- `GET /api/payroll` - Get payroll records
- `POST /api/payroll/generate` - Generate payroll
- `GET /api/payroll/employee/{id}` - Get employee payroll

## Project Structure

```
src/
├── main/
│   ├── java/com/hrms/
│   │   ├── config/          # Configuration classes
│   │   ├── controller/      # REST controllers
│   │   ├── dto/            # Data Transfer Objects
│   │   ├── model/          # Entity classes
│   │   ├── repository/     # Data access layer
│   │   ├── security/       # Security configuration
│   │   ├── service/        # Business logic layer
│   │   └── HrmsApplication.java
│   └── resources/
│       ├── static/         # Static web assets
│       │   ├── css/        # Stylesheets
│       │   └── js/         # JavaScript files
│       ├── templates/      # HTML templates
│       ├── application.properties
│       └── schema.sql      # Database schema
└── test/                   # Test classes
```

## Database Schema

The system uses the following main entities:
- **Users**: System users with authentication
- **Roles**: User roles and permissions
- **Employees**: Employee information and records
- **Leave Requests**: Leave management
- **Payrolls**: Salary and payroll data
- **Job Postings**: Recruitment job postings
- **Job Applications**: Candidate applications

## Security Features

- JWT-based authentication
- Role-based access control
- Password encryption with BCrypt
- CORS configuration for API access
- Session management
- Secure API endpoints

## Development

### Running in Development Mode
```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### Running Tests
```bash
mvn test
```

### Building for Production
```bash
mvn clean package -Pprod
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/new-feature`)
3. Commit your changes (`git commit -am 'Add new feature'`)
4. Push to the branch (`git push origin feature/new-feature`)
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

## Future Enhancements

- Mobile application
- Advanced reporting with PDF generation
- Integration with external HR systems
- Biometric attendance integration
- Advanced analytics and AI insights
- Multi-language support
- Cloud deployment options
