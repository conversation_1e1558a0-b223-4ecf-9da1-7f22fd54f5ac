/* Custom CSS for HRMS */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 10px 15px;
    border-radius: 5px;
    margin: 2px 10px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.sidebar .nav-link.active {
    background-color: #007bff;
    color: #fff;
}

.sidebar .nav-link i {
    margin-right: 8px;
}

main {
    margin-left: 240px;
    padding: 20px;
}

@media (max-width: 767.98px) {
    .sidebar {
        top: 5rem;
    }
    
    main {
        margin-left: 0;
    }
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #eee;
    font-weight: 600;
}

.btn {
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background-color: #007bff;
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
}

.form-control {
    border-radius: 5px;
    border: 1px solid #ddd;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.section {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.alert {
    border-radius: 5px;
    border: none;
}

.text-primary {
    color: #007bff !important;
}

.text-success {
    color: #28a745 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.bg-primary {
    background-color: #007bff !important;
}

.bg-success {
    background-color: #28a745 !important;
}

.bg-warning {
    background-color: #ffc107 !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.bg-info {
    background-color: #17a2b8 !important;
}

/* Dashboard Cards */
.dashboard-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.dashboard-card h4 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.dashboard-card p {
    font-size: 1rem;
    opacity: 0.9;
}

/* Employee Cards */
.employee-card {
    border-left: 4px solid #007bff;
    transition: all 0.3s ease;
}

.employee-card:hover {
    border-left-color: #28a745;
    transform: translateX(5px);
}

/* Status Badges */
.status-active {
    background-color: #28a745;
}

.status-inactive {
    background-color: #6c757d;
}

.status-pending {
    background-color: #ffc107;
}

.status-approved {
    background-color: #28a745;
}

.status-rejected {
    background-color: #dc3545;
}

/* Modal Styles */
.modal-content {
    border-radius: 10px;
    border: none;
}

.modal-header {
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
}

.modal-footer {
    border-top: 1px solid #eee;
    background-color: #f8f9fa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        margin-left: -240px;
    }
    
    .sidebar.show {
        margin-left: 0;
    }
    
    main {
        margin-left: 0;
        padding: 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .btn,
    .navbar {
        display: none !important;
    }
    
    main {
        margin-left: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
