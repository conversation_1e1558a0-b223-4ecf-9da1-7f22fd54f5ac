// HRMS Application JavaScript

// Global variables
let currentUser = null;
let authToken = null;
const API_BASE_URL = '/hrms/api';

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    checkAuthStatus();
    initializeEventListeners();
});

// Check if user is authenticated
function checkAuthStatus() {
    authToken = localStorage.getItem('authToken');
    currentUser = JSON.parse(localStorage.getItem('currentUser') || 'null');
    
    if (authToken && currentUser) {
        showMainApp();
        loadDashboardData();
    } else {
        showLoginSection();
    }
}

// Show login section
function showLoginSection() {
    hideAllSections();
    document.getElementById('login-section').style.display = 'block';
    document.getElementById('sidebar').style.display = 'none';
}

// Show main application
function showMainApp() {
    document.getElementById('login-section').style.display = 'none';
    document.getElementById('sidebar').style.display = 'block';
    document.getElementById('username').textContent = currentUser.username;
    showSection('dashboard');
}

// Initialize event listeners
function initializeEventListeners() {
    // Login form
    document.getElementById('loginForm').addEventListener('submit', handleLogin);
    
    // Navigation links
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

// Handle login
async function handleLogin(e) {
    e.preventDefault();
    
    const username = document.getElementById('loginUsername').value;
    const password = document.getElementById('loginPassword').value;
    
    try {
        const response = await fetch(`${API_BASE_URL}/auth/signin`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });
        
        if (response.ok) {
            const data = await response.json();
            authToken = data.accessToken;
            currentUser = {
                id: data.id,
                username: data.username,
                email: data.email,
                roles: data.roles
            };
            
            localStorage.setItem('authToken', authToken);
            localStorage.setItem('currentUser', JSON.stringify(currentUser));
            
            showMainApp();
            loadDashboardData();
            showAlert('Login successful!', 'success');
        } else {
            const error = await response.json();
            showAlert(error.message || 'Login failed', 'danger');
        }
    } catch (error) {
        console.error('Login error:', error);
        showAlert('Network error. Please try again.', 'danger');
    }
}

// Logout
function logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    authToken = null;
    currentUser = null;
    showLoginSection();
    showAlert('Logged out successfully', 'info');
}

// Show section
function showSection(sectionId) {
    hideAllSections();
    document.getElementById(sectionId).style.display = 'block';
    
    // Load section-specific data
    switch(sectionId) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'employees':
            loadEmployeesData();
            break;
        case 'payroll':
            loadPayrollData();
            break;
        case 'leaves':
            loadLeavesData();
            break;
        case 'recruitment':
            loadRecruitmentData();
            break;
        case 'reports':
            loadReportsData();
            break;
    }
}

// Hide all sections
function hideAllSections() {
    document.querySelectorAll('.section').forEach(section => {
        if (section.id !== 'login-section') {
            section.style.display = 'none';
        }
    });
}

// Load dashboard data
async function loadDashboardData() {
    try {
        // Load dashboard statistics
        await Promise.all([
            loadEmployeeStats(),
            loadLeaveStats(),
            loadRecruitmentStats()
        ]);
        
        // Initialize charts
        initializeDepartmentChart();
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showAlert('Error loading dashboard data', 'danger');
    }
}

// Load employee statistics
async function loadEmployeeStats() {
    try {
        const response = await apiCall('/employees');
        if (response) {
            const employees = response;
            document.getElementById('totalEmployees').textContent = employees.length;
            
            const activeEmployees = employees.filter(emp => emp.employmentStatus === 'ACTIVE');
            document.getElementById('activeEmployees').textContent = activeEmployees.length;
        }
    } catch (error) {
        console.error('Error loading employee stats:', error);
    }
}

// Load leave statistics
async function loadLeaveStats() {
    // Placeholder for leave statistics
    document.getElementById('pendingLeaves').textContent = '5';
}

// Load recruitment statistics
async function loadRecruitmentStats() {
    // Placeholder for recruitment statistics
    document.getElementById('openPositions').textContent = '3';
}

// Initialize department chart
function initializeDepartmentChart() {
    const ctx = document.getElementById('departmentChart').getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['IT', 'HR', 'Finance', 'Marketing', 'Operations'],
            datasets: [{
                data: [30, 10, 15, 20, 25],
                backgroundColor: [
                    '#007bff',
                    '#28a745',
                    '#ffc107',
                    '#dc3545',
                    '#17a2b8'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Load employees data
function loadEmployeesData() {
    // Placeholder for employees data loading
    console.log('Loading employees data...');
}

// Load payroll data
function loadPayrollData() {
    // Placeholder for payroll data loading
    console.log('Loading payroll data...');
}

// Load leaves data
function loadLeavesData() {
    // Placeholder for leaves data loading
    console.log('Loading leaves data...');
}

// Load recruitment data
function loadRecruitmentData() {
    // Placeholder for recruitment data loading
    console.log('Loading recruitment data...');
}

// Load reports data
function loadReportsData() {
    // Placeholder for reports data loading
    console.log('Loading reports data...');
}

// Generic API call function
async function apiCall(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
        }
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(url, finalOptions);
        
        if (response.status === 401) {
            // Token expired or invalid
            logout();
            return null;
        }
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('API call error:', error);
        throw error;
    }
}

// Show alert message
function showAlert(message, type = 'info') {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());
    
    // Create new alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert alert at the top of main content
    const main = document.querySelector('main');
    main.insertBefore(alertDiv, main.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString();
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

function capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

// Export functions for use in other modules
window.HRMS = {
    showSection,
    logout,
    apiCall,
    showAlert,
    formatDate,
    formatCurrency,
    capitalizeFirst
};
